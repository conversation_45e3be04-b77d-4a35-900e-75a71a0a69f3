#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品信息提取器
从产品文件夹中的markdown文件提取信息，生成符合模板格式的JSON文件
"""

import os
import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse


class ProductProcessor:
    def __init__(self, base_dir: str = ".", config_file: str = "config.json"):
        self.base_dir = Path(base_dir)
        self.products = []
        self.config = self.load_config(config_file)

    def load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"警告: 配置文件 {config_file} 不存在，使用默认配置")
            return self.get_default_config()
        except json.JSONDecodeError as e:
            print(f"警告: 配置文件格式错误: {e}，使用默认配置")
            return self.get_default_config()

    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "default_settings": {
                "base_url": "https://example.com",
                "default_stock_status": "instock",
                "default_status": "publish"
            },
            "category_mapping": {},
            "material_mapping": {},
            "feature_keywords": {},
            "application_templates": {"default": ["Replacement Part", "Repair Service"]}
        }

    def extract_basic_info(self, content: str) -> Dict[str, Any]:
        """从markdown内容中提取基本信息"""
        basic_info = {}
        
        # 提取产品ID
        product_id_match = re.search(r'\*\*产品ID\*\*:\s*([^\n]+)', content)
        if product_id_match:
            basic_info['sku'] = product_id_match.group(1).strip()
        
        # 提取产品名称
        product_name_match = re.search(r'\*\*产品名称\*\*:\s*([^\n]+)', content)
        if product_name_match:
            basic_info['name'] = product_name_match.group(1).strip()
        
        # 提取价格
        price_match = re.search(r'\*\*价格\*\*:\s*([0-9.]+)', content)
        if price_match:
            basic_info['regular_price'] = float(price_match.group(1))
            
        return basic_info
    
    def extract_description(self, content: str) -> str:
        """提取产品描述"""
        # 查找产品描述部分
        desc_match = re.search(r'## 产品描述\s*\n\n(.*?)(?=\n##|\n$)', content, re.DOTALL)
        if desc_match:
            description = desc_match.group(1).strip()
            # 清理描述文本，移除多余的空格和换行
            description = re.sub(r'\s+', ' ', description)
            return description
        return ""
    
    def extract_compatibility(self, content: str, category: str = "default") -> List[str]:
        """提取兼容性信息作为应用场景"""
        applications = []

        # 从兼容性信息中提取
        compat_match = re.search(r'通用 - 兼容性\s*\|\s*([^\n|]+)', content)
        if compat_match:
            compat_info = compat_match.group(1).strip()
            applications.append(f"Compatible with {compat_info}")

        # 使用配置文件中的应用场景模板
        application_templates = self.config.get("application_templates", {})
        template_apps = application_templates.get(category, application_templates.get("default", []))
        applications.extend(template_apps)

        return applications
    
    def extract_features(self, content: str) -> List[str]:
        """从描述中提取特性"""
        features = []
        content_lower = content.lower()

        # 使用配置文件中的特性关键词映射
        feature_keywords = self.config.get("feature_keywords", {})
        for keyword, feature in feature_keywords.items():
            if keyword.lower() in content_lower:
                if feature not in features:
                    features.append(feature)

        # 如果没有找到特性，使用默认特性
        return features if features else ["High Quality", "Durable", "Easy Installation"]
    
    def extract_materials(self, content: str) -> List[str]:
        """根据产品类型推断材料"""
        name_lower = content.lower()

        # 使用配置文件中的材料映射
        material_mapping = self.config.get("material_mapping", {})
        for keyword, materials in material_mapping.items():
            if keyword.lower() in name_lower:
                return materials

        # 默认材料
        return ["Electronic Components", "Plastic", "Metal"]
    
    def generate_slug(self, name: str) -> str:
        """生成URL友好的slug"""
        # 移除特殊字符，转换为小写，用连字符连接
        slug = re.sub(r'[^\w\s-]', '', name.lower())
        slug = re.sub(r'[\s_-]+', '-', slug)
        slug = slug.strip('-')
        return slug
    
    def get_category(self, name: str) -> str:
        """根据产品名称确定分类"""
        name_lower = name.lower()

        # 使用配置文件中的分类映射
        category_mapping = self.config.get("category_mapping", {})
        for keyword, category in category_mapping.items():
            if keyword.lower() in name_lower:
                return category

        # 默认分类
        return "accessories"
    
    def get_image_urls(self, folder_path: Path, base_url: str = "https://example.com") -> List[str]:
        """获取图片URL列表"""
        images = []
        images_dir = folder_path / "images"

        if images_dir.exists():
            # 按文件名排序，确保主图片在前
            img_files = sorted(images_dir.glob("*"))
            for img_file in img_files:
                if img_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                    # 生成图片URL，使用URL编码处理中文文件名
                    from urllib.parse import quote
                    encoded_folder = quote(folder_path.name)
                    encoded_filename = quote(img_file.name)
                    img_url = f"{base_url}/{encoded_folder}/images/{encoded_filename}"
                    images.append(img_url)

        return images
    
    def process_product_folder(self, folder_path: Path) -> Optional[Dict[str, Any]]:
        """处理单个产品文件夹"""
        # 查找markdown文件
        md_files = list(folder_path.glob("*.md"))
        if not md_files:
            print(f"警告: 在 {folder_path} 中未找到markdown文件")
            return None
        
        md_file = md_files[0]  # 使用第一个找到的markdown文件
        
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"错误: 无法读取文件 {md_file}: {e}")
            return None
        
        # 提取信息
        basic_info = self.extract_basic_info(content)
        
        # 获取产品名称和分类
        product_name = basic_info.get('name', folder_path.name)
        category = self.get_category(product_name)

        # 构建产品数据
        product = {
            "name": product_name,
            "slug": self.generate_slug(product_name),
            "sku": basic_info.get('sku', f"SKU-{folder_path.name}"),
            "short_desc": f"High quality {product_name}",
            "description": self.extract_description(content),
            "regular_price": basic_info.get('regular_price', 0),
            "stock_status": self.config.get("default_settings", {}).get("default_stock_status", "instock"),
            "status": self.config.get("default_settings", {}).get("default_status", "publish"),
            "category": category,
            "images": self.get_image_urls(folder_path, self.config.get("default_settings", {}).get("base_url", "https://example.com")),
            "features": self.extract_features(content),
            "applications": self.extract_compatibility(content, category),
            "materials": self.extract_materials(product_name)
        }
        
        return product
    
    def process_all_folders(self) -> List[Dict[str, Any]]:
        """处理所有产品文件夹"""
        products = []
        
        for item in self.base_dir.iterdir():
            if item.is_dir() and not item.name.startswith('.') and item.name != '__pycache__':
                print(f"处理文件夹: {item.name}")
                product = self.process_product_folder(item)
                if product:
                    products.append(product)
                    print(f"✓ 成功处理: {product['name']}")
                else:
                    print(f"✗ 处理失败: {item.name}")
        
        return products
    
    def save_to_json(self, products: List[Dict[str, Any]], output_file: str = "products_upload.json"):
        """保存产品数据到JSON文件"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(products, f, ensure_ascii=False, indent=2)
            print(f"✓ 成功生成JSON文件: {output_file}")
            print(f"  共处理 {len(products)} 个产品")
        except Exception as e:
            print(f"错误: 无法保存JSON文件: {e}")


def main():
    parser = argparse.ArgumentParser(description='批量处理产品文件夹，生成上传用的JSON文件')
    parser.add_argument('--input-dir', '-i', default='.', help='输入目录路径 (默认: 当前目录)')
    parser.add_argument('--output', '-o', default='products_upload.json', help='输出JSON文件名 (默认: products_upload.json)')
    parser.add_argument('--config', '-c', default='config.json', help='配置文件路径 (默认: config.json)')
    parser.add_argument('--base-url', '-u', help='图片基础URL (覆盖配置文件设置)')

    args = parser.parse_args()

    print("=== 产品信息提取器 ===")
    print(f"输入目录: {args.input_dir}")
    print(f"输出文件: {args.output}")
    print(f"配置文件: {args.config}")
    if args.base_url:
        print(f"图片基础URL: {args.base_url}")
    print()

    processor = ProductProcessor(args.input_dir, args.config)

    # 如果命令行指定了base_url，覆盖配置文件设置
    if args.base_url:
        processor.config.setdefault("default_settings", {})["base_url"] = args.base_url

    products = processor.process_all_folders()

    if products:
        processor.save_to_json(products, args.output)
        print(f"\n处理完成! 生成了 {len(products)} 个产品的数据")
    else:
        print("未找到任何有效的产品数据")


if __name__ == "__main__":
    main()
