#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品信息提取器 - 简单GUI版本
"""

import tkinter as tk
from tkinter import messagebox
import os
import threading
from pathlib import Path

# 直接导入处理器，避免subprocess问题
try:
    from product_processor import ProductProcessor
    PROCESSOR_AVAILABLE = True
except ImportError:
    PROCESSOR_AVAILABLE = False


class SimpleProductGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("产品信息提取器")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # 居中显示
        self.center_window()
        self.setup_ui()
        
    def center_window(self):
        """居中显示窗口"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_ui(self):
        """设置界面"""
        # 标题
        title_label = tk.Label(self.root, text="产品信息批量提取器", 
                              font=("Arial", 18, "bold"), fg="blue")
        title_label.pack(pady=20)
        
        # 说明
        info_text = """这个工具可以批量处理产品文件夹，
从markdown文件中提取产品信息，
生成符合模板格式的JSON文件。

请确保产品文件夹结构正确：
产品文件夹/
├── 产品名称.md
└── images/
    └── 图片文件"""
        
        info_label = tk.Label(self.root, text=info_text, justify=tk.LEFT, 
                             font=("Arial", 10), wraplength=450)
        info_label.pack(pady=20, padx=20)
        
        # 按钮区域
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=30)
        
        # 主处理按钮
        self.process_btn = tk.Button(button_frame, text="开始处理产品", 
                                    command=self.start_processing,
                                    font=("Arial", 12, "bold"),
                                    bg="#4CAF50", fg="white",
                                    width=20, height=2)
        self.process_btn.pack(pady=10)
        
        # 其他按钮
        tk.Button(button_frame, text="打开当前文件夹", 
                 command=self.open_folder,
                 font=("Arial", 10),
                 bg="#FF9800", fg="white",
                 width=20).pack(pady=5)
        
        tk.Button(button_frame, text="查看帮助", 
                 command=self.show_help,
                 font=("Arial", 10),
                 width=20).pack(pady=5)
        
        # 状态栏
        self.status_label = tk.Label(self.root, text="就绪", 
                                    relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 检查处理器是否可用
        if not PROCESSOR_AVAILABLE:
            self.status_label.config(text="警告: 处理器模块不可用")
            self.process_btn.config(state="disabled")
            
    def start_processing(self):
        """开始处理"""
        if not PROCESSOR_AVAILABLE:
            messagebox.showerror("错误", "处理器模块不可用，请检查product_processor.py文件")
            return
            
        # 检查产品文件夹
        current_dir = Path(".")
        product_folders = [d for d in current_dir.iterdir() 
                          if d.is_dir() and not d.name.startswith('.') 
                          and d.name != '__pycache__']
        
        if not product_folders:
            messagebox.showwarning("警告", "当前目录下没有找到产品文件夹！")
            return
            
        # 在新线程中处理
        self.process_btn.config(state="disabled", text="处理中...")
        self.status_label.config(text="正在处理...")
        
        thread = threading.Thread(target=self.process_products)
        thread.daemon = True
        thread.start()
        
    def process_products(self):
        """处理产品（后台线程）"""
        try:
            # 创建处理器
            processor = ProductProcessor(".", "config.json")
            
            # 处理所有产品
            products = processor.process_all_folders()
            
            if products:
                # 保存结果
                processor.save_to_json(products, "products_upload.json")
                
                # 显示成功消息
                self.root.after(0, lambda: messagebox.showinfo(
                    "成功", 
                    f"处理完成！\n共生成 {len(products)} 个产品数据\n\n输出文件: products_upload.json"
                ))
                self.root.after(0, lambda: self.status_label.config(text=f"完成 - 处理了 {len(products)} 个产品"))
            else:
                self.root.after(0, lambda: messagebox.showwarning("警告", "未找到任何有效的产品数据"))
                self.root.after(0, lambda: self.status_label.config(text="未找到有效产品"))
                
        except Exception as e:
            error_msg = f"处理失败: {str(e)}"
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
            self.root.after(0, lambda: self.status_label.config(text="处理失败"))
            
        finally:
            # 恢复按钮状态
            self.root.after(0, lambda: self.process_btn.config(state="normal", text="开始处理产品"))
            
    def open_folder(self):
        """打开当前文件夹"""
        try:
            import sys
            if sys.platform == "win32":
                os.startfile(".")
            elif sys.platform == "darwin":
                os.system("open .")
            else:
                os.system("xdg-open .")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件夹: {str(e)}")
            
    def show_help(self):
        """显示帮助"""
        help_text = """使用步骤：

1. 准备产品文件夹
   每个产品文件夹应包含：
   - 一个markdown文件（.md）
   - 一个images文件夹，包含产品图片

2. 点击"开始处理产品"按钮

3. 等待处理完成

4. 查看生成的 products_upload.json 文件

文件夹结构示例：
产品文件夹/
├── 产品名称.md
└── images/
    ├── 图片1.jpg
    └── 图片2.jpg

Markdown文件格式：
# 产品名称

## 基本信息
- **产品ID**: SKU123
- **产品名称**: 产品名称
- **价格**: 99.99

## 产品描述
详细描述..."""
        
        # 创建帮助窗口
        help_window = tk.Toplevel(self.root)
        help_window.title("使用帮助")
        help_window.geometry("600x500")
        help_window.resizable(False, False)
        
        # 居中显示
        help_window.transient(self.root)
        help_window.grab_set()
        
        # 帮助文本
        text_widget = tk.Text(help_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)
        
        # 关闭按钮
        close_btn = tk.Button(help_window, text="关闭", command=help_window.destroy)
        close_btn.pack(pady=10)
        
    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    app = SimpleProductGUI()
    app.run()


if __name__ == "__main__":
    main()
