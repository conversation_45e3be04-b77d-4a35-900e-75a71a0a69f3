# 产品信息批量处理系统

## 项目概述

这是一个专门为您的产品上传需求开发的批量处理系统，可以从产品文件夹中的markdown文件自动提取信息，并生成符合您的product-template.json格式的JSON文件，用于批量上传到电商平台。

## 🚀 主要功能

- ✅ **批量处理**: 一次性处理多个产品文件夹
- ✅ **智能提取**: 自动从markdown文件提取产品信息
- ✅ **图片收集**: 自动收集并生成图片URL
- ✅ **智能分类**: 根据产品名称自动分类
- ✅ **配置灵活**: 支持自定义分类、材料、特性映射
- ✅ **数据验证**: 内置验证工具确保数据质量
- ✅ **中文支持**: 完美支持中文文件名和内容

## 📁 项目文件结构

```
productsuploade/
├── product_processor.py      # 主处理程序
├── validate_products.py      # 数据验证工具
├── config.json              # 配置文件
├── run_processor.bat        # Windows批处理脚本
├── product-template.json    # 产品模板文件
├── products_upload.json     # 生成的产品数据
├── 项目说明.md              # 本文件
├── 使用示例.md              # 详细使用说明
├── README_产品处理器.md     # 程序使用文档
└── 适用于iPhone_16_喇叭/    # 示例产品文件夹
    ├── 适用于iPhone_16_喇叭.md
    └── images/
        ├── 适用于iPhone_16_喇叭.jpg
        ├── 适用于iPhone_16_喇叭_2.jpg
        └── 适用于iPhone_16_喇叭_3.jpg
```

## 🛠️ 使用方法

### 方法1: 直接运行（推荐）
```bash
python product_processor.py
```

### 方法2: Windows批处理（最简单）
双击运行 `run_processor.bat`

### 方法3: 自定义参数
```bash
# 指定输出文件
python product_processor.py -o my_products.json

# 指定图片基础URL
python product_processor.py -u https://mystore.com/images

# 使用自定义配置
python product_processor.py -c my_config.json
```

## 📋 产品文件夹要求

每个产品文件夹应包含：

1. **一个markdown文件** (建议与文件夹同名)
2. **一个images文件夹** 包含产品图片

### Markdown文件格式示例：
```markdown
# 产品名称

## 基本信息
- **产品ID**: EDA007202404
- **产品名称**: 适用于iPhone 16 喇叭
- **价格**: 4.49

## 产品描述
详细的产品描述内容...

## 包装参数
| 参数名称 | 参数值 |
|----------|--------|
| 通用 - 兼容性 | Apple:iPhone 16 |
```

## ⚙️ 配置文件说明

`config.json` 文件允许您自定义：

- **产品分类映射**: 根据关键词自动分类
- **材料映射**: 根据产品类型推断材料
- **特性关键词**: 从描述中提取产品特性
- **应用场景模板**: 为不同分类设置应用场景
- **默认设置**: 图片URL、库存状态等

## 🔍 数据验证

使用验证工具检查生成的JSON文件：

```bash
python validate_products.py products_upload.json
```

验证工具会检查：
- 必需字段完整性
- 数据类型正确性
- URL格式有效性
- 价格数值合理性

## 📊 输出格式

生成的JSON文件包含以下字段：
- `name`: 产品名称
- `slug`: URL友好标识
- `sku`: 产品SKU
- `short_desc`: 简短描述
- `description`: 详细描述
- `regular_price`: 价格
- `stock_status`: 库存状态
- `status`: 发布状态
- `category`: 产品分类
- `images`: 图片URL数组
- `features`: 特性数组
- `applications`: 应用场景数组
- `materials`: 材料数组

## 🎯 智能分类系统

程序会根据产品名称自动分类：

| 关键词 | 分类 |
|--------|------|
| 喇叭/speaker | speakers |
| 屏幕/screen/lcd | displays |
| 电池/battery | batteries |
| 摄像头/camera | cameras |
| 充电器/charger | chargers |
| 数据线/cable | cables |
| 外壳/case | cases |

## 🔧 故障排除

### 常见问题：

1. **找不到markdown文件**
   - 确保每个产品文件夹都有.md文件

2. **价格提取失败**
   - 检查价格格式：`- **价格**: 4.49`

3. **图片路径错误**
   - 确保images文件夹存在
   - 支持格式：jpg, jpeg, png, gif, webp

4. **中文编码问题**
   - 确保markdown文件使用UTF-8编码

## 📈 处理流程

1. **扫描目录** → 找到所有产品文件夹
2. **读取markdown** → 提取产品信息
3. **收集图片** → 生成图片URL列表
4. **智能分析** → 确定分类、特性、材料
5. **生成JSON** → 输出符合模板格式的数据
6. **验证数据** → 确保数据完整性

## 🎉 使用效果

- ⏱️ **效率提升**: 从手动处理改为自动批量处理
- 🎯 **准确性**: 智能提取，减少人工错误
- 🔄 **可重复**: 配置一次，重复使用
- 📊 **标准化**: 确保所有产品数据格式统一
- 🌐 **国际化**: 支持中英文混合处理

## 💡 扩展建议

1. **添加更多产品类型**: 在config.json中扩展分类映射
2. **自定义特性提取**: 根据您的产品特点添加关键词
3. **批量图片处理**: 可以集成图片压缩、格式转换功能
4. **数据导出**: 支持导出为CSV、Excel等格式
5. **API集成**: 直接上传到电商平台API

这个系统已经为您的产品批量处理需求量身定制，可以大大提高您的工作效率！
