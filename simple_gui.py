#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的产品处理器GUI启动器
如果主GUI有问题，可以使用这个简化版本
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import subprocess
import os
import sys


class SimpleGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("产品信息提取器 - 简化版")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # 居中显示窗口
        self.center_window()
        
        self.setup_ui()
        
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_label = tk.Label(self.root, text="产品信息批量提取器", 
                              font=("Arial", 18, "bold"), fg="blue")
        title_label.pack(pady=20)
        
        # 说明文本
        info_text = """这个工具可以批量处理产品文件夹，
从markdown文件中提取产品信息，
生成符合模板格式的JSON文件。

请确保您的产品文件夹结构如下：
产品文件夹/
├── 产品名称.md
└── images/
    ├── 图片1.jpg
    └── 图片2.jpg"""
        
        info_label = tk.Label(self.root, text=info_text, justify=tk.LEFT, 
                             font=("Arial", 10), wraplength=450)
        info_label.pack(pady=20, padx=20)
        
        # 按钮框架
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=30)
        
        # 主要功能按钮
        process_btn = tk.Button(button_frame, text="开始处理产品", 
                               command=self.run_processor,
                               font=("Arial", 12, "bold"),
                               bg="#4CAF50", fg="white",
                               width=15, height=2)
        process_btn.pack(pady=10)
        
        # 其他功能按钮
        validate_btn = tk.Button(button_frame, text="验证结果", 
                                command=self.validate_results,
                                font=("Arial", 10),
                                bg="#2196F3", fg="white",
                                width=15)
        validate_btn.pack(pady=5)
        
        open_dir_btn = tk.Button(button_frame, text="打开文件夹", 
                                command=self.open_current_dir,
                                font=("Arial", 10),
                                bg="#FF9800", fg="white",
                                width=15)
        open_dir_btn.pack(pady=5)
        
        # 高级选项按钮
        advanced_btn = tk.Button(button_frame, text="高级选项", 
                                command=self.show_advanced,
                                font=("Arial", 10),
                                bg="#9C27B0", fg="white",
                                width=15)
        advanced_btn.pack(pady=5)
        
        # 帮助按钮
        help_btn = tk.Button(button_frame, text="使用帮助", 
                            command=self.show_help,
                            font=("Arial", 10),
                            width=15)
        help_btn.pack(pady=5)
        
        # 状态栏
        self.status_label = tk.Label(self.root, text="就绪", 
                                    relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X)
        
    def run_processor(self):
        """运行产品处理器"""
        try:
            self.status_label.config(text="正在处理...")
            self.root.update()
            
            # 检查是否存在产品文件夹
            current_dir = os.getcwd()
            product_folders = [d for d in os.listdir(current_dir) 
                             if os.path.isdir(d) and not d.startswith('.') 
                             and d != '__pycache__']
            
            if not product_folders:
                messagebox.showwarning("警告", "当前目录下没有找到产品文件夹！\n\n请确保产品文件夹与程序在同一目录下。")
                self.status_label.config(text="就绪")
                return
            
            # 运行处理器
            result = subprocess.run([sys.executable, "product_processor.py"],
                                  capture_output=True, text=True, encoding='utf-8', errors='ignore')
            
            if result.returncode == 0:
                # 成功
                output_lines = result.stdout.strip().split('\n')
                success_msg = "处理完成！\n\n"
                for line in output_lines:
                    if "共处理" in line and "个产品" in line:
                        success_msg += line + "\n"
                        break
                
                success_msg += f"\n输出文件: products_upload.json"
                messagebox.showinfo("成功", success_msg)
                self.status_label.config(text="处理完成")
            else:
                # 失败
                error_msg = result.stderr if result.stderr else result.stdout
                messagebox.showerror("错误", f"处理失败：\n{error_msg}")
                self.status_label.config(text="处理失败")
                
        except FileNotFoundError:
            messagebox.showerror("错误", "找不到 product_processor.py 文件！\n请确保所有文件都在同一目录下。")
            self.status_label.config(text="就绪")
        except Exception as e:
            messagebox.showerror("错误", f"运行时发生错误：\n{str(e)}")
            self.status_label.config(text="就绪")
            
    def validate_results(self):
        """验证处理结果"""
        json_file = "products_upload.json"
        if not os.path.exists(json_file):
            messagebox.showerror("错误", f"找不到输出文件: {json_file}\n请先运行产品处理。")
            return
            
        try:
            result = subprocess.run([sys.executable, "validate_products.py", json_file],
                                  capture_output=True, text=True, encoding='utf-8', errors='ignore')
            
            if result.returncode == 0:
                messagebox.showinfo("验证结果", "✅ 验证通过！\n\n数据格式正确，可以用于上传。")
            else:
                messagebox.showerror("验证结果", f"❌ 验证失败！\n\n{result.stdout}")
                
        except FileNotFoundError:
            messagebox.showerror("错误", "找不到验证工具！")
        except Exception as e:
            messagebox.showerror("错误", f"验证时发生错误：\n{str(e)}")
            
    def open_current_dir(self):
        """打开当前目录"""
        try:
            if sys.platform == "win32":
                os.startfile(".")
            elif sys.platform == "darwin":
                os.system("open .")
            else:
                os.system("xdg-open .")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开目录：\n{str(e)}")
            
    def show_advanced(self):
        """显示高级选项"""
        try:
            # 尝试启动完整的GUI
            subprocess.Popen([sys.executable, "product_processor_gui.py"])
            messagebox.showinfo("提示", "已启动高级GUI界面！")
        except FileNotFoundError:
            messagebox.showerror("错误", "找不到高级GUI程序文件！")
        except Exception as e:
            messagebox.showerror("错误", f"启动高级界面失败：\n{str(e)}")
            
    def show_help(self):
        """显示帮助信息"""
        help_text = """使用步骤：

1. 准备产品文件夹
   每个产品文件夹应包含：
   - 一个markdown文件（.md）
   - 一个images文件夹，包含产品图片

2. 点击"开始处理产品"按钮

3. 等待处理完成

4. 查看生成的 products_upload.json 文件

文件夹结构示例：
产品文件夹/
├── 产品名称.md
└── images/
    ├── 图片1.jpg
    └── 图片2.jpg

Markdown文件格式：
# 产品名称

## 基本信息
- **产品ID**: SKU123
- **产品名称**: 产品名称
- **价格**: 99.99

## 产品描述
详细描述...

如有问题，请查看详细文档。"""
        
        # 创建帮助窗口
        help_window = tk.Toplevel(self.root)
        help_window.title("使用帮助")
        help_window.geometry("600x500")
        help_window.resizable(False, False)
        
        # 居中显示
        help_window.transient(self.root)
        help_window.grab_set()
        
        # 帮助文本
        text_widget = tk.Text(help_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)
        
        # 关闭按钮
        close_btn = tk.Button(help_window, text="关闭", command=help_window.destroy)
        close_btn.pack(pady=10)
        
    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    app = SimpleGUI()
    app.run()


if __name__ == "__main__":
    main()
