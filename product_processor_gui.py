#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品信息提取器 - GUI版本
带有图形用户界面的产品批量处理工具
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
from pathlib import Path
import json

# 导入原有的处理器类
from product_processor import ProductProcessor


class ProductProcessorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("产品信息提取器 v1.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 变量
        self.input_dir = tk.StringVar(value=os.getcwd())
        self.output_file = tk.StringVar(value="products_upload.json")
        self.config_file = tk.StringVar(value="config.json")
        self.base_url = tk.StringVar(value="https://example.com")
        self.processing = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="产品信息批量提取器", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 输入目录选择
        ttk.Label(main_frame, text="产品文件夹目录:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.input_dir, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="浏览", command=self.browse_input_dir).grid(row=1, column=2, pady=5)
        
        # 输出文件
        ttk.Label(main_frame, text="输出JSON文件:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_file, width=50).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="选择", command=self.browse_output_file).grid(row=2, column=2, pady=5)
        
        # 配置文件
        ttk.Label(main_frame, text="配置文件:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.config_file, width=50).grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="选择", command=self.browse_config_file).grid(row=3, column=2, pady=5)
        
        # 图片基础URL
        ttk.Label(main_frame, text="图片基础URL:").grid(row=4, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.base_url, width=50).grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        
        # 分隔线
        ttk.Separator(main_frame, orient='horizontal').grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=3, pady=10)
        
        # 按钮
        self.process_btn = ttk.Button(button_frame, text="开始处理", command=self.start_processing, style="Accent.TButton")
        self.process_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="验证结果", command=self.validate_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="打开输出目录", command=self.open_output_dir).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关于", command=self.show_about).pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="就绪", foreground="green")
        self.status_label.grid(row=8, column=0, columnspan=3, pady=5)
        
        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="5")
        log_frame.grid(row=9, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(9, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清空日志按钮
        ttk.Button(log_frame, text="清空日志", command=self.clear_log).grid(row=1, column=0, pady=5)
        
    def browse_input_dir(self):
        """浏览输入目录"""
        directory = filedialog.askdirectory(initialdir=self.input_dir.get())
        if directory:
            self.input_dir.set(directory)
            
    def browse_output_file(self):
        """选择输出文件"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialdir=os.path.dirname(self.output_file.get()),
            initialfile=os.path.basename(self.output_file.get())
        )
        if filename:
            self.output_file.set(filename)
            
    def browse_config_file(self):
        """选择配置文件"""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialdir=os.path.dirname(self.config_file.get()),
            initialfile=os.path.basename(self.config_file.get())
        )
        if filename:
            self.config_file.set(filename)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        
    def update_status(self, message, color="black"):
        """更新状态"""
        self.status_label.config(text=message, foreground=color)
        
    def start_processing(self):
        """开始处理（在新线程中）"""
        if self.processing:
            return
            
        # 验证输入
        if not os.path.exists(self.input_dir.get()):
            messagebox.showerror("错误", "输入目录不存在！")
            return
            
        # 在新线程中运行处理
        self.processing = True
        self.process_btn.config(state="disabled", text="处理中...")
        self.progress.start()
        self.clear_log()
        
        thread = threading.Thread(target=self.process_products)
        thread.daemon = True
        thread.start()
        
    def process_products(self):
        """处理产品（后台线程）"""
        try:
            self.update_status("正在处理...", "blue")
            self.log_message("=== 开始处理产品文件夹 ===")
            self.log_message(f"输入目录: {self.input_dir.get()}")
            self.log_message(f"输出文件: {self.output_file.get()}")
            self.log_message(f"配置文件: {self.config_file.get()}")
            self.log_message(f"图片基础URL: {self.base_url.get()}")
            self.log_message("")
            
            # 创建处理器
            processor = ProductProcessor(self.input_dir.get(), self.config_file.get())
            
            # 设置基础URL
            if self.base_url.get():
                processor.config.setdefault("default_settings", {})["base_url"] = self.base_url.get()
            
            # 处理所有文件夹
            products = []
            input_path = Path(self.input_dir.get())
            
            for item in input_path.iterdir():
                if item.is_dir() and not item.name.startswith('.') and item.name != '__pycache__':
                    self.log_message(f"处理文件夹: {item.name}")
                    product = processor.process_product_folder(item)
                    if product:
                        products.append(product)
                        self.log_message(f"✓ 成功处理: {product['name']}")
                    else:
                        self.log_message(f"✗ 处理失败: {item.name}")
            
            if products:
                # 保存结果
                processor.save_to_json(products, self.output_file.get())
                self.log_message("")
                self.log_message(f"✓ 成功生成JSON文件: {self.output_file.get()}")
                self.log_message(f"  共处理 {len(products)} 个产品")
                
                self.update_status(f"处理完成！生成了 {len(products)} 个产品", "green")
                
                # 显示成功消息
                self.root.after(0, lambda: messagebox.showinfo("成功", f"处理完成！\n共生成 {len(products)} 个产品数据\n\n输出文件: {self.output_file.get()}"))
            else:
                self.log_message("未找到任何有效的产品数据")
                self.update_status("未找到有效产品", "orange")
                self.root.after(0, lambda: messagebox.showwarning("警告", "未找到任何有效的产品数据"))
                
        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            self.update_status("处理失败", "red")
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
            
        finally:
            # 恢复UI状态
            self.processing = False
            self.progress.stop()
            self.process_btn.config(state="normal", text="开始处理")
            
    def validate_results(self):
        """验证处理结果"""
        output_file = self.output_file.get()
        if not os.path.exists(output_file):
            messagebox.showerror("错误", f"输出文件不存在: {output_file}")
            return
            
        try:
            # 导入验证器
            from validate_products import ProductValidator
            
            validator = ProductValidator()
            result = validator.validate_json_file(output_file)
            
            # 显示验证结果
            if result['valid']:
                msg = f"✅ 验证通过！\n\n"
                msg += f"产品总数: {result['product_count']}\n"
                if result['summary']:
                    msg += f"平均价格: {result['summary']['average_price']:.2f}\n"
                    msg += f"总价值: {result['summary']['total_value']:.2f}\n"
                messagebox.showinfo("验证结果", msg)
            else:
                msg = f"❌ 验证失败！\n\n发现 {len(result['errors'])} 个错误:\n\n"
                msg += "\n".join(result['errors'][:5])  # 只显示前5个错误
                if len(result['errors']) > 5:
                    msg += f"\n... 还有 {len(result['errors']) - 5} 个错误"
                messagebox.showerror("验证结果", msg)
                
        except ImportError:
            messagebox.showerror("错误", "验证模块不可用")
        except Exception as e:
            messagebox.showerror("错误", f"验证过程中发生错误: {str(e)}")
            
    def open_output_dir(self):
        """打开输出目录"""
        output_dir = os.path.dirname(os.path.abspath(self.output_file.get()))
        try:
            if sys.platform == "win32":
                os.startfile(output_dir)
            elif sys.platform == "darwin":
                os.system(f"open '{output_dir}'")
            else:
                os.system(f"xdg-open '{output_dir}'")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开目录: {str(e)}")
            
    def show_about(self):
        """显示关于信息"""
        about_text = """产品信息提取器 v1.0

功能特性:
• 批量处理产品文件夹
• 自动提取markdown信息
• 智能产品分类
• 生成标准JSON格式
• 支持中文文件名
• 数据验证功能

开发: AI Assistant
版本: 1.0.0
"""
        messagebox.showinfo("关于", about_text)


def main():
    # 创建主窗口
    root = tk.Tk()
    
    # 设置主题（如果支持）
    try:
        style = ttk.Style()
        style.theme_use('clam')  # 使用现代主题
    except:
        pass
    
    # 创建应用
    app = ProductProcessorGUI(root)
    
    # 运行主循环
    root.mainloop()


if __name__ == "__main__":
    main()
