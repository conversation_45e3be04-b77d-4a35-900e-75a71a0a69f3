@echo off
title Product Processor GUI

echo Starting Product Processor GUI...
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found
    echo Please install Python from https://www.python.org/
    pause
    exit /b 1
)

echo Python OK
echo.

REM Try to start full GUI first
echo Trying to start full GUI...
python product_processor_gui.py
if errorlevel 1 (
    echo.
    echo Full GUI failed, trying simple GUI...
    python simple_gui.py
)

echo.
echo Press any key to exit...
pause >nul
