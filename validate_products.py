#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品数据验证工具
验证生成的JSON文件是否符合模板要求
"""

import json
import argparse
from typing import List, Dict, Any
from pathlib import Path


class ProductValidator:
    def __init__(self):
        self.required_fields = [
            'name', 'slug', 'sku', 'short_desc', 'description',
            'regular_price', 'stock_status', 'status', 'category',
            'images', 'features', 'applications', 'materials'
        ]
        self.valid_stock_statuses = ['instock', 'outofstock', 'onbackorder']
        self.valid_statuses = ['publish', 'draft', 'private']
        
    def validate_product(self, product: Dict[str, Any], index: int) -> List[str]:
        """验证单个产品数据"""
        errors = []
        
        # 检查必需字段
        for field in self.required_fields:
            if field not in product:
                errors.append(f"产品 {index + 1}: 缺少必需字段 '{field}'")
            elif product[field] is None or product[field] == "":
                errors.append(f"产品 {index + 1}: 字段 '{field}' 不能为空")
        
        # 验证具体字段
        if 'name' in product:
            if not isinstance(product['name'], str) or len(product['name'].strip()) == 0:
                errors.append(f"产品 {index + 1}: 产品名称必须是非空字符串")
        
        if 'slug' in product:
            if not isinstance(product['slug'], str) or len(product['slug'].strip()) == 0:
                errors.append(f"产品 {index + 1}: slug必须是非空字符串")
        
        if 'sku' in product:
            if not isinstance(product['sku'], str) or len(product['sku'].strip()) == 0:
                errors.append(f"产品 {index + 1}: SKU必须是非空字符串")
        
        if 'regular_price' in product:
            if not isinstance(product['regular_price'], (int, float)) or product['regular_price'] < 0:
                errors.append(f"产品 {index + 1}: 价格必须是非负数字")
        
        if 'stock_status' in product:
            if product['stock_status'] not in self.valid_stock_statuses:
                errors.append(f"产品 {index + 1}: 库存状态必须是 {self.valid_stock_statuses} 之一")
        
        if 'status' in product:
            if product['status'] not in self.valid_statuses:
                errors.append(f"产品 {index + 1}: 状态必须是 {self.valid_statuses} 之一")
        
        if 'images' in product:
            if not isinstance(product['images'], list):
                errors.append(f"产品 {index + 1}: 图片必须是数组")
            elif len(product['images']) == 0:
                errors.append(f"产品 {index + 1}: 至少需要一张图片")
            else:
                for i, img in enumerate(product['images']):
                    if not isinstance(img, str) or not img.startswith(('http://', 'https://')):
                        errors.append(f"产品 {index + 1}: 图片 {i + 1} 必须是有效的URL")
        
        if 'features' in product:
            if not isinstance(product['features'], list):
                errors.append(f"产品 {index + 1}: 特性必须是数组")
            elif len(product['features']) == 0:
                errors.append(f"产品 {index + 1}: 至少需要一个特性")
        
        if 'applications' in product:
            if not isinstance(product['applications'], list):
                errors.append(f"产品 {index + 1}: 应用场景必须是数组")
        
        if 'materials' in product:
            if not isinstance(product['materials'], list):
                errors.append(f"产品 {index + 1}: 材料必须是数组")
        
        return errors
    
    def validate_json_file(self, file_path: str) -> Dict[str, Any]:
        """验证JSON文件"""
        result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'product_count': 0,
            'summary': {}
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except FileNotFoundError:
            result['valid'] = False
            result['errors'].append(f"文件不存在: {file_path}")
            return result
        except json.JSONDecodeError as e:
            result['valid'] = False
            result['errors'].append(f"JSON格式错误: {e}")
            return result
        
        if not isinstance(data, list):
            result['valid'] = False
            result['errors'].append("JSON文件必须包含产品数组")
            return result
        
        if len(data) == 0:
            result['warnings'].append("JSON文件中没有产品数据")
            return result
        
        result['product_count'] = len(data)
        
        # 验证每个产品
        all_errors = []
        categories = {}
        total_price = 0
        
        for i, product in enumerate(data):
            if not isinstance(product, dict):
                all_errors.append(f"产品 {i + 1}: 必须是对象")
                continue
            
            product_errors = self.validate_product(product, i)
            all_errors.extend(product_errors)
            
            # 统计信息
            if 'category' in product:
                categories[product['category']] = categories.get(product['category'], 0) + 1
            
            if 'regular_price' in product and isinstance(product['regular_price'], (int, float)):
                total_price += product['regular_price']
        
        result['errors'] = all_errors
        result['valid'] = len(all_errors) == 0
        
        # 生成摘要
        result['summary'] = {
            'categories': categories,
            'average_price': total_price / len(data) if len(data) > 0 else 0,
            'total_value': total_price
        }
        
        return result
    
    def print_validation_result(self, result: Dict[str, Any], file_path: str):
        """打印验证结果"""
        print("=" * 60)
        print(f"产品数据验证报告: {file_path}")
        print("=" * 60)
        
        if result['valid']:
            print("✅ 验证通过！")
        else:
            print("❌ 验证失败！")
        
        print(f"\n📊 统计信息:")
        print(f"   产品总数: {result['product_count']}")
        
        if result['summary']:
            summary = result['summary']
            print(f"   平均价格: {summary['average_price']:.2f}")
            print(f"   总价值: {summary['total_value']:.2f}")
            
            if summary['categories']:
                print(f"   产品分类:")
                for category, count in summary['categories'].items():
                    print(f"     - {category}: {count} 个")
        
        if result['warnings']:
            print(f"\n⚠️  警告 ({len(result['warnings'])}):")
            for warning in result['warnings']:
                print(f"   - {warning}")
        
        if result['errors']:
            print(f"\n❌ 错误 ({len(result['errors'])}):")
            for error in result['errors']:
                print(f"   - {error}")
        
        print("\n" + "=" * 60)


def main():
    parser = argparse.ArgumentParser(description='验证产品JSON文件')
    parser.add_argument('file', help='要验证的JSON文件路径')
    parser.add_argument('--quiet', '-q', action='store_true', help='只显示错误，不显示详细信息')
    
    args = parser.parse_args()
    
    validator = ProductValidator()
    result = validator.validate_json_file(args.file)
    
    if not args.quiet:
        validator.print_validation_result(result, args.file)
    
    # 返回适当的退出码
    exit(0 if result['valid'] else 1)


if __name__ == "__main__":
    main()
