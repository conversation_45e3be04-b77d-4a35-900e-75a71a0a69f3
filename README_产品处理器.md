# 产品信息提取器使用说明

这个程序可以批量处理产品文件夹，从markdown文件中提取产品信息，并生成符合模板格式的JSON文件用于上传。

## 功能特性

- 🔄 批量处理多个产品文件夹
- 📝 自动从markdown文件提取产品信息
- 🖼️ 自动收集产品图片路径
- 📊 生成符合模板格式的JSON文件
- 🏷️ 智能分类和标签生成
- 🔗 自动生成URL友好的slug

## 使用方法

### 基本用法

```bash
python product_processor.py
```

这将处理当前目录下的所有产品文件夹，生成 `products_upload.json` 文件。

### 高级用法

```bash
# 指定输入目录和输出文件
python product_processor.py -i /path/to/products -o my_products.json

# 指定图片基础URL
python product_processor.py --base-url https://mystore.com/images

# 查看帮助
python product_processor.py --help
```

### 参数说明

- `-i, --input-dir`: 输入目录路径 (默认: 当前目录)
- `-o, --output`: 输出JSON文件名 (默认: products_upload.json)
- `-u, --base-url`: 图片基础URL (默认: https://example.com)

## 文件夹结构要求

每个产品文件夹应该包含：

```
产品文件夹名/
├── 产品文件夹名.md    # 产品信息markdown文件
└── images/           # 产品图片文件夹
    ├── 图片1.jpg
    ├── 图片2.jpg
    └── ...
```

## Markdown文件格式要求

程序会从markdown文件中提取以下信息：

```markdown
# 产品名称

## 基本信息

- **产品ID**: EDA007202404
- **产品名称**: 适用于iPhone 16 喇叭
- **价格**: 4.49

## 产品描述

产品的详细描述内容...

## 包装参数

| 参数名称 | 参数值 |
|----------|--------|
| 通用 - 兼容性 | Apple:iPhone 16 |
| ... | ... |
```

## 输出JSON格式

生成的JSON文件将包含以下字段：

```json
[
  {
    "name": "产品名称",
    "slug": "product-slug",
    "sku": "产品SKU",
    "short_desc": "简短描述",
    "description": "详细描述",
    "regular_price": 价格,
    "stock_status": "instock",
    "status": "publish",
    "category": "产品分类",
    "images": ["图片URL1", "图片URL2"],
    "features": ["特性1", "特性2"],
    "applications": ["应用场景1", "应用场景2"],
    "materials": ["材料1", "材料2"]
  }
]
```

## 智能分类

程序会根据产品名称自动分类：

- 喇叭/Speaker → speakers
- 屏幕/Screen/LCD → displays  
- 电池/Battery → batteries
- 摄像头/Camera → cameras
- 充电器/Charger → chargers
- 其他 → accessories

## 注意事项

1. 确保每个产品文件夹都有对应的markdown文件
2. markdown文件中的基本信息格式要正确
3. 图片文件夹名称必须是 `images`
4. 支持的图片格式：jpg, jpeg, png, gif, webp

## 示例运行

```bash
$ python product_processor.py
=== 产品信息提取器 ===
输入目录: .
输出文件: products_upload.json
图片基础URL: https://example.com

处理文件夹: 适用于iPhone_16_喇叭
✓ 成功处理: 适用于iPhone 16 喇叭

✓ 成功生成JSON文件: products_upload.json
  共处理 1 个产品

处理完成! 生成了 1 个产品的数据
```

## 故障排除

1. **找不到markdown文件**: 确保文件夹中有.md文件
2. **编码错误**: 确保markdown文件使用UTF-8编码
3. **价格提取失败**: 检查价格格式是否为数字
4. **图片路径错误**: 确保images文件夹存在且包含图片文件
