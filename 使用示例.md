# 产品信息提取器使用示例

## 快速开始

1. **准备产品文件夹结构**
```
项目根目录/
├── product_processor.py      # 主程序
├── config.json              # 配置文件
├── 适用于iPhone_16_喇叭/     # 产品文件夹1
│   ├── 适用于iPhone_16_喇叭.md
│   └── images/
│       ├── 适用于iPhone_16_喇叭.jpg
│       ├── 适用于iPhone_16_喇叭_2.jpg
│       └── 适用于iPhone_16_喇叭_3.jpg
├── iPhone_15_LCD屏幕/        # 产品文件夹2
│   ├── iPhone_15_LCD屏幕.md
│   └── images/
│       └── screen.jpg
└── Samsung_S24_电池/         # 产品文件夹3
    ├── Samsung_S24_电池.md
    └── images/
        └── battery.jpg
```

2. **运行程序**
```bash
# 基本用法
python product_processor.py

# 指定输出文件
python product_processor.py -o my_products.json

# 指定图片基础URL
python product_processor.py -u https://mystore.com/images

# 使用自定义配置文件
python product_processor.py -c my_config.json
```

3. **Windows用户可以直接双击运行**
```
run_processor.bat
```

## 配置文件自定义

### 修改图片基础URL
```json
{
  "default_settings": {
    "base_url": "https://your-domain.com/images"
  }
}
```

### 添加新的产品分类
```json
{
  "category_mapping": {
    "耳机": "headphones",
    "headphone": "headphones",
    "鼠标": "mouse",
    "mouse": "mouse"
  }
}
```

### 添加新的材料映射
```json
{
  "material_mapping": {
    "耳机": ["Plastic", "Metal", "Foam"],
    "鼠标": ["Plastic", "Electronic Components", "Rubber"]
  }
}
```

### 自定义特性关键词
```json
{
  "feature_keywords": {
    "无线": "Wireless",
    "蓝牙": "Bluetooth",
    "防尘": "Dust Resistant",
    "抗震": "Shock Resistant"
  }
}
```

## Markdown文件模板

创建新产品时，请按以下格式编写markdown文件：

```markdown
# 产品名称

## 基本信息

- **产品ID**: YOUR_SKU_CODE
- **产品名称**: 具体的产品名称
- **价格**: 99.99

## 产品描述

详细的产品描述，包括：
1. 产品功能
2. 使用方法
3. 注意事项
4. 安装建议

## 包装参数

| 参数名称 | 参数值 |
|----------|--------|
| 通用 - 兼容性 | 兼容设备信息 |
| 单个产品重量 | 0.1kgs / 0.22lb |
| 单个产品尺寸 | 10cm * 5cm * 2cm |

## 产品图片

图片保存在 `images/` 文件夹中
```

## 批量处理多个产品

1. **创建多个产品文件夹**，每个文件夹包含：
   - 一个markdown文件（建议与文件夹同名）
   - 一个images文件夹，包含产品图片

2. **运行处理器**：
```bash
python product_processor.py
```

3. **检查生成的JSON文件**：
   - 文件名：`products_upload.json`
   - 格式：符合product-template.json的结构
   - 内容：包含所有处理成功的产品

## 常见问题解决

### 1. 中文文件名问题
程序已自动处理中文文件名的URL编码，无需担心。

### 2. 图片路径问题
- 确保images文件夹存在
- 支持的图片格式：jpg, jpeg, png, gif, webp
- 图片会按文件名排序，建议主图使用较小的序号

### 3. 价格提取失败
确保markdown中的价格格式为：
```markdown
- **价格**: 4.49
```

### 4. 分类不正确
在config.json中添加相应的关键词映射：
```json
{
  "category_mapping": {
    "你的产品关键词": "目标分类"
  }
}
```

## 输出文件说明

生成的JSON文件包含以下字段：
- `name`: 产品名称
- `slug`: URL友好的产品标识
- `sku`: 产品SKU代码
- `short_desc`: 简短描述
- `description`: 详细描述
- `regular_price`: 常规价格
- `stock_status`: 库存状态
- `status`: 发布状态
- `category`: 产品分类
- `images`: 图片URL数组
- `features`: 产品特性数组
- `applications`: 应用场景数组
- `materials`: 材料数组

这个JSON文件可以直接用于电商平台的批量上传功能。
