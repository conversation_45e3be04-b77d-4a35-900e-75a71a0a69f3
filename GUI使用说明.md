# 产品信息提取器 - GUI版本使用说明

## 🎉 新增功能

现在您有了**三种**使用方式：

### 1. 🖥️ 完整图形界面（推荐）
- 功能最全面的GUI界面
- 支持所有自定义选项
- 实时处理日志显示
- 内置验证功能

### 2. 🎯 简化图形界面
- 简单易用的界面
- 一键处理功能
- 适合快速操作

### 3. ⚡ 命令行版本
- 原有的命令行工具
- 支持批处理脚本
- 适合自动化场景

## 🚀 快速开始

### 最简单的方式：
1. **双击运行** `启动器.bat`
2. **选择选项1** 启动图形界面
3. **点击"开始处理"** 按钮
4. **等待完成** 查看结果

### 创建桌面快捷方式：
1. 双击运行 `创建桌面快捷方式.bat`
2. 桌面会出现"产品信息提取器"快捷方式
3. 以后直接双击桌面快捷方式即可使用

## 📱 GUI界面功能详解

### 完整GUI界面功能：

#### 🔧 基本设置
- **产品文件夹目录**: 选择包含产品文件夹的目录
- **输出JSON文件**: 指定生成的JSON文件名和位置
- **配置文件**: 选择自定义配置文件
- **图片基础URL**: 设置图片的基础URL地址

#### 🎛️ 操作按钮
- **开始处理**: 批量处理所有产品文件夹
- **验证结果**: 验证生成的JSON文件格式
- **打开输出目录**: 快速打开结果文件所在目录
- **关于**: 查看程序信息

#### 📊 实时反馈
- **进度条**: 显示处理进度
- **状态显示**: 实时显示当前状态
- **处理日志**: 详细的处理过程记录
- **清空日志**: 清除日志内容

### 简化GUI界面功能：

#### 🎯 一键操作
- **开始处理产品**: 一键处理当前目录下的所有产品
- **验证结果**: 验证生成的JSON文件
- **打开文件夹**: 打开当前工作目录
- **高级选项**: 启动完整GUI界面
- **使用帮助**: 查看详细帮助信息

## 📋 使用步骤详解

### 步骤1: 准备产品文件夹
```
项目目录/
├── 产品1文件夹/
│   ├── 产品1.md
│   └── images/
│       ├── 图片1.jpg
│       └── 图片2.jpg
├── 产品2文件夹/
│   ├── 产品2.md
│   └── images/
│       └── 图片.jpg
└── 程序文件...
```

### 步骤2: 启动程序
- **方式1**: 双击 `启动器.bat` → 选择GUI选项
- **方式2**: 双击 `启动GUI.bat` 直接启动完整GUI
- **方式3**: 双击 `simple_gui.py` 启动简化GUI

### 步骤3: 配置选项（完整GUI）
1. **选择输入目录**: 点击"浏览"选择产品文件夹所在目录
2. **设置输出文件**: 指定生成的JSON文件名
3. **配置图片URL**: 设置您的图片服务器地址
4. **选择配置文件**: 使用自定义配置（可选）

### 步骤4: 开始处理
1. 点击"开始处理"按钮
2. 观察进度条和日志输出
3. 等待处理完成提示

### 步骤5: 验证结果
1. 点击"验证结果"按钮
2. 查看验证报告
3. 确认数据格式正确

## 🎨 界面预览

### 完整GUI界面包含：
- 📁 文件选择区域
- ⚙️ 配置选项区域  
- 🔘 操作按钮区域
- 📊 进度显示区域
- 📝 日志输出区域

### 简化GUI界面包含：
- 📋 程序说明
- 🔘 主要功能按钮
- 📞 帮助信息
- 📊 状态显示

## ⚠️ 注意事项

### 系统要求：
- ✅ Windows 7/8/10/11
- ✅ Python 3.6+
- ✅ tkinter模块（通常随Python安装）

### 文件要求：
- ✅ 每个产品文件夹必须包含markdown文件
- ✅ images文件夹必须存在且包含图片
- ✅ markdown文件格式要正确

### 常见问题：
1. **GUI无法启动**: 检查Python和tkinter是否正确安装
2. **找不到产品**: 确保产品文件夹与程序在同一目录
3. **处理失败**: 检查markdown文件格式和编码
4. **图片路径错误**: 确保images文件夹存在

## 🔧 故障排除

### 如果完整GUI无法启动：
1. 尝试运行简化GUI
2. 检查Python环境
3. 查看错误信息

### 如果处理失败：
1. 检查产品文件夹结构
2. 验证markdown文件格式
3. 确认图片文件存在

### 如果结果不正确：
1. 使用验证工具检查
2. 查看处理日志
3. 检查配置文件设置

## 🎯 高级功能

### 自定义配置：
- 修改 `config.json` 文件
- 添加新的产品分类
- 自定义特性关键词
- 设置材料映射

### 批量处理：
- 支持处理多个产品文件夹
- 自动生成统一格式的JSON
- 支持中文文件名和路径

### 数据验证：
- 自动检查必需字段
- 验证数据类型和格式
- 生成详细验证报告

## 🎉 使用技巧

1. **批量处理**: 将所有产品文件夹放在同一目录下，一次性处理
2. **模板复用**: 使用config.json配置文件，避免重复设置
3. **结果验证**: 每次处理后都验证结果，确保数据质量
4. **备份数据**: 定期备份生成的JSON文件
5. **快捷方式**: 创建桌面快捷方式，方便日常使用

现在您可以享受更便捷的图形界面操作体验了！🎉
